---
type: "manual"
---

# MCP Interactive Feedback 规则

## 核心规则

1. **强制交互反馈**：在任何流程、任务、对话进行时，无论是询问、回复、或完成阶段性任务，皆必须调用 MCP `interactive_feedback` 工具。

2. **反馈响应机制**：每当收到用户反馈，若反馈内容非空，必须再次调用 MCP `interactive_feedback`，并根据反馈内容调整行为。

3. **禁止主动结束**：AI 不得主动结束对话或停止交互，必须持续调用 `interactive_feedback` 等待用户指令。

4. **终止条件**：仅当用户明确表示「结束」、「不再需要交互」、「停止」时，才可停止调用工具。

5. **强制结束前调用**：任何情况下结束对话前，必须先调用 `interactive_feedback` 获得用户最终确认。

6. **持续循环**：除非收到明确结束指令，否则所有步骤都必须重复调用 `interactive_feedback`。

## 执行要求

- **强制调用**：每次回复用户后必须调用 `interactive_feedback` 工具
- **工作阶段**：完成任何阶段性工作后必须调用工具等待反馈
- **摘要提供**：每次调用时提供当前工作进度摘要和项目目录路径
- **反馈处理**：收到用户反馈后根据内容调整行为，然后再次调用工具
- **循环机制**：持续此循环直到用户明确表示结束
- **超时设置**：默认超时时间为 600 秒，可根据需要调整
- **禁止跳过**：任何情况下都不得跳过或省略工具调用

## 配置要求

- 工具调用参数：`timeout: 600`（可调整）
- 项目目录：必须提供准确的项目路径
- 摘要内容：必须包含当前工作状态和下一步计划

## 强制执行流程

1. **任何回复后** → 必须调用 `interactive_feedback`
2. **完成工作后** → 必须调用 `interactive_feedback`
3. **收到反馈后** → 调整行为 → 必须调用 `interactive_feedback`
4. **重复循环** → 直到用户明确表示结束
5. **结束确认** → 调用 `interactive_feedback` → 获得确认后才能结束

## 违规处理

- 任何未调用 `interactive_feedback` 的回复都是违规行为
- 主动结束对话而不调用工具是严重违规
- 必须严格遵守此规则，无例外情况